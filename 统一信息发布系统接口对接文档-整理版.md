统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 本专科生外聘教职工基本信息


接口描述：本专科生外聘教职工基本信息
请求 URL：
/open_api/customization/tgxjgbzkswpjzgjbxx/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
GH	string	否	否	工号	body
SZDWDM	string	否	否	所在单位代码	body
XM	string	否	否	姓名	body
XBM	string	否	否	性别码	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



GH	Nullable(String)	工号



SZDWDM	Nullable(String)	所在单位代码



XM	Nullable(String)	姓名



XBM	Nullable(String)	性别码



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 研究生教室基本信息


接口描述：研究生教室基本信息
请求 URL：
/open_api/customization/tgxjxyjsjsjbxx/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
JASDM	string	否	否	教室代码	body
JASMC	string	否	否	教室名称	body
JASLXDM	string	否	否	教室类型代码	body
JXLDM	string	否	否	教学楼代码	body
JXLMC	string	否	否	教学楼名称	body
XQDM	string	否	否	校区代码	body
SKZWS	string	否	否	上课座位数	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



JASDM	String	教室代码



JASMC	Nullable(String)	教室名称



JASLXDM	Nullable(String)	教室类型代码



JXLDM	Nullable(String)	教学楼代码



JXLMC	Nullable(String)	教学楼名称



XQDM	Nullable(String)	校区代码



SKZWS	Nullable(String)	上课座位数



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 本专科生教室基本信息


接口描述：本专科生教室基本信息
请求 URL：
/open_api/customization/tgxjxbzksjsjbxx/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
JASDM	string	否	否	教室代码	body
JASMC	string	否	否	教室名称	body
JASLXDM	string	否	否	教室类型代码	body
JXLDM	string	否	否	教学楼代码	body
JXLMC	string	否	否	教学楼名称	body
XQDM	string	否	否	校区代码	body
SKZWS	string	否	否	上课座位数	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



JASDM	String	教室代码



JASMC	Nullable(String)	教室名称



JASLXDM	Nullable(String)	教室类型代码



JXLDM	Nullable(String)	教学楼代码



JXLMC	Nullable(String)	教学楼名称



XQDM	Nullable(String)	校区代码



SKZWS	Nullable(String)	上课座位数



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 教职工基本信息


接口描述：
请求 URL：
/open_api/customization/tgxjgjzgjbxx/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
GH	string	否	否	工号	body
SZDWBM	string	否	否	所在单位编码	body
XM	string	否	否	姓名	body
XBM	string	否	否	性别码	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



GH	String	工号



SZDWBM	Nullable(String)	所在单位编码



XM	Nullable(String)	姓名



XBM	Nullable(String)	性别码



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 研究生教师授课信息


接口描述：
请求 URL：
/open_api/customization/tgxjxyjsjsskxx/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
WYBS	string	否	否	唯一标识	body
JXBID	string	否	否	教学班 ID	body
XNXQDM	string	否	否	学年学期代码	body
ZC	string	否	否	周次	body
ZCMC	string	否	否	周次名称	body
XQ	string	否	否	星期	body
KSJC	string	否	否	开始节次	body
JSJC	string	否	否	结束节次	body
JASDM	string	否	否	教室代码	body
JASMC	string	否	否	教室名称	body
JSXM	string	否	否	教师姓名	body
KCDM	string	否	否	课程代码 (T_KCGL_KCXX_KCJBXX)	body
KCMC	string	否	否	课程名称	body
JSGH	string	否	否	教师工号	body
JXBMC	string	否	否	教学班名称	body
KSSJ	string	否	否	开始时间	body
JSSJ	string	否	否	计算时间	body
YXDM	string	否	否	院系编码	body
XKRS	string	否	否	选课人数	body
XQDM	string	否	否	校区	body
XSZYJC	string	否	否	学生专业简称	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



WYBS	String	唯一标识



JXBID	Nullable(String)	教学班 ID



XNXQDM	Nullable(String)	学年学期代码



ZC	Nullable(String)	周次



ZCMC	Nullable(String)	周次名称



XQ	Nullable(String)	星期



KSJC	Nullable(String)	开始节次



JSJC	Nullable(String)	结束节次



JASDM	Nullable(String)	教室代码



JASMC	Nullable(String)	教室名称



JSXM	Nullable(String)	教师姓名



KCDM	Nullable(String)	课程代码 (T_KCGL_KCXX_KCJBXX)



KCMC	Nullable(String)	课程名称



JSGH	Nullable(String)	教师工号



JXBMC	Nullable(String)	教学班名称



KSSJ	Nullable(String)	开始时间



JSSJ	Nullable(String)	计算时间



YXDM	Nullable(String)	院系编码



XKRS	Nullable(String)	选课人数



XQDM	Nullable(String)	校区



XSZYJC	Nullable(String)	学生专业简称



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 本专科生教师授课信息 V11


接口描述：本专科生教师授课信息，包含调停课信息
请求 URL：
/open_api/customization/tgxjxbzksjsskxxv/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
JXBID	string	否	否	教学班 ID	body
GH	string	否	否	工号	body
XM	string	否	否	姓名	body
XNXQDM	string	否	否	学年学期代码	body
KCDM	string	否	否	课程代码	body
KCMC	string	否	否	课程名称	body
SKZC	string	否	否	上课周次	body
SKXQ	string	否	否	上课星期	body
KSJC	string	否	否	开始节次	body
JSJC	string	否	否	结束节次	body
JASDM	string	否	否	教室代码	body
JASMC	string	否	否	教室名称	body
XQDM	string	否	否	校区代码	body
SKBJDM	string	否	否	上课班级代码	body
SKBJMC	string	否	否	上课班级名称	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



JXBID	Nullable(String)	教学班 ID



GH	Nullable(String)	工号



XM	Nullable(String)	姓名



XNXQDM	Nullable(String)	学年学期代码



KCDM	Nullable(String)	课程代码



KCMC	Nullable(String)	课程名称



SKZC	Nullable(String)	上课周次



SKXQ	Nullable(String)	上课星期



KSJC	Nullable(String)	开始节次



JSJC	Nullable(String)	结束节次



JASDM	Nullable(String)	教室代码



JASMC	Nullable(String)	教室名称



XQDM	Nullable(String)	校区代码



SKBJDM	Nullable(String)	上课班级代码



SKBJMC	Nullable(String)	上课班级名称



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 研究生选课信息


接口描述：
请求 URL：
/open_api/customization/tgxjxyjsxkxx/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
WYBS	string	否	否	唯一标识	body
XH	string	否	否	学号	body
JXBID	string	否	否	教学班 ID	body
XNXQDM	string	否	否	学年学期代码	body
KCDM	string	否	否	课程代码	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



WYBS	String	唯一标识



XH	Nullable(String)	学号



JXBID	Nullable(String)	教学班 ID



XNXQDM	Nullable(String)	学年学期代码



KCDM	Nullable(String)	课程代码



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 本专科生选课信息


接口描述：本专科生选课信息
请求 URL：
/open_api/customization/tgxjxbzksxkxx/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
XNXQDM	string	否	否	学年学期代码 (T_YJSBZ_XNXQ)	body
WYBS	string	否	否	唯一标识	body
XH	string	否	否	学号	body
KCDM	string	否	否	课程代码	body
BJDM	string	否	否	班级代码	body
JXBID	string	否	否	教学班 ID	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



XNXQDM	Nullable(String)	学年学期代码 (T_YJSBZ_XNXQ)



WYBS	String	唯一标识



XH	Nullable(String)	学号



KCDM	Nullable(String)	课程代码



BJDM	Nullable(String)	班级代码



JXBID	Nullable(String)	教学班 ID



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 学年学期


接口描述：
请求 URL：
/open_api/customization/tyzsjdmxbxnxqm/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
XNXQDM	string	否	否	学年学期代码	body
XNDM	string	否	否	学年代码	body
XQDM	string	否	否	学期代码	body
XNXQMC	string	否	否	学年学期名称	body
XQMC	string	否	否	学期名称	body
SFDQXQ	string	否	否	是否当前学期	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



XNXQDM	String	学年学期代码



XNDM	Nullable(String)	学年代码



XQDM	Nullable(String)	学期代码



XNXQMC	Nullable(String)	学年学期名称



XQMC	Nullable(String)	学期名称



SFDQXQ	Nullable(String)	是否当前学期



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',
'result':{
'access_token':'xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
'expires_in':7200
}
}
3.3 学校校区


接口描述：
请求 URL：
/open_api/customization/tgxxxxqjbxx_alpha/full
请求方式：GET/POST
请求参数：


参数名	类型	必填	模糊查询	描述	位置
XQBM	string	否	否	校区编码	body
XQMC	string	否	否	校区名称	body
page	string	否	否	当前页数	body
per_page	string	否	否	每页最大数据量	body


返回参数：


参数名	类型	描述	默认值	代码表

code	string	返回代码



description	string	返回说明



message	string	返回消息



result	object	返回结果



data	object	返回数据



XQBM	String	校区编码



XQMC	Nullable(String)	校区名称



data_struct	string	数据结构



encrypted_field	string	加密的字段



max_page	string	最大页数



page	string	当前页数



per_page	string	每页最大数据量



total	string	数据量





返回示例:
{
code:10000,
description:'api 请求成功 ',
message:'ok',
result:{
data:[],
data_struct:{},
encrypted_field:'',
max_page:'',
page:1,
per_page:3,
total:1
}
uuid:'afd77b22f7d04d338fff115adf1bb537'
}
更多接口操作指导，请到系统下载获取通用接口文档
新增接口对接文档内容


以下为新增的 5 个接口对接文档内容：
来自文件：统一信息发布系统接口对接文档 - 1754879318.md


统一信息发布系统接口对接文档
1、API 文件简介


简介：数据中台数据接口对接是基于数据中台平台提供的数据开放服务。接口格式设计完全遵循 RESTful，您可以通过接口对接规范，依照文档进行接口开发获取数据。
2、应用详情


应用名称：统一信息发布系统
key：202508055161e5648ee471de11f0a0b3ea6b9558de3d2756
secret：864a9f36f8cc8a19be33ac6dc039efeb5e420361
3、接口列表
3.1 接口访问地址


https://dmp.ynu.edu.cn
3.2 用户获取 token


接口描述：用于用户获取 token，开发者可根据应用的 key、secret 生成 Token，调用数据时，需要传入 token 用于用户身份校验，
数据调用传参示例：（当参数仅仅是键值对形式时，可以直接构建 URL 访问，非键值对形式的参数建议构建 json 请求体之后通过 post 访问。每个 API 都支持 GET,POST 两种传参模式）
/open_api/authentication/get_access_token
请求方式：GET
请求参数：


参数名	类型	必填	模糊查询	描述	位置
key	string	是	否	APP KEY	path
secret	string	是	否	APP SECRET	path


返回参数说明:


参数名	类型	描述	默认值	代码表
code	string	返回代码


message	string	返回消息


description	string	返回说明


result	object	返回结果


access_token	string	调用凭证


expires_in	string	有效期




返回示例:
{
'code':10000,
'message':'ok',
'description':'api 请求成功 ',

……

内容过长，仅显示前1300行。建议下载完整文件查看全部内容。